SELECT top(100)[i].[Name] AS IntName,
                 [s].[Name] AS StudyName,
               [soi].[Name] AS StudyIntName,
              [soip].[PropertyName],
              [soip].[PropertyValue]
         FROM [Rrbm].[StudyOrganizationIntegrationProperties] soip
   INNER JOIN [Rrbm].[StudyOrganizationIntegrations] soi ON [soi].[Id] = [soip].[StudyOrganizationIntegrationId]
   INNER JOIN [Rrbm].[OrganizationIntegrations] oi ON [oi].[Id] = [soi].[OrganizationIntegrationId]
   INNER JOIN [Rrbm].[TenantIntegrations] ti ON [ti].[Id] = [oi].[TenantIntegrationId]
   INNER JOIN [Rrbm].[Integrations] i ON [i].[Id] = [ti].[IntegrationId]
   INNER JOIN [Rrbm].[Studies] s ON [s].[Id] = [soi].[StudyId]
          WHERE [s].[Id] in (1209, 1106) 
          AND
        [i].[Name] like '%RaveAPI'
          AND [soip].[PropertyName] LIKE '%ControlTableList%'
     ORDER BY [soip].[PropertyName]

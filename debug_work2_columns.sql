-- Debug script to check what columns are actually available in work2.sql
-- This will help identify the column name issue

DECLARE @StudyId INT = 851;
DECLARE @DATEFORMAT VARCHAR(25) = 'yyyy-MM-dd';
DECLARE @TodaysDate DATE = GETDATE();

-- Test just the CTE part to see what columns are available
WITH FullVisitSchedule AS (
SELECT 
      [SiteCode]
    , [PatientCode]
    , [PatientStatus]
    , [ItemId]
    , [IntervalValue]    
    , [VisitStatus] = '01_Test'  -- Simplified for debugging
    , [FolderName]
    , [FolderOID]
    , [WindowReferenceFrom]
    , [SVYN]
    , [VisitEntered]
    , [ProjectedStartWinDate_] = FORMAT([ProjectedStartWinDate], @DATEFORMAT)
    , [VisitDate_] = FORMAT([VisitDate], @DATEFORMAT)
    , [ProjectedVisitDate_] = FORMAT([ProjectedVisitDate], @DATEFORMAT)
    , [ProjectedEndWinDate_] = FORMAT([ProjectedEndWinDate], @DATEFORMAT)
    , [ProjectedOverDueDate_] = FORMAT([ProjectedOverDueDate], @DATEFORMAT)
    , [ScreenedDateString] 
    , [ScreenFailedDateString]
    , [EnrolledDateString]
    , [EarlyTerminatedDateString]
    , [CompletedDateString]
    -- Also include the raw date columns for WHERE clause
    , [VisitDate]  -- Raw DATE column
    , [ProjectedVisitDate]  -- Raw DATE column

   FROM rrbm.StudyMetrics AS FullVisitSchedule 
    CROSS APPLY OPENJSON([FullVisitSchedule].[CustomProperties], '$.Properties') 
    WITH ( 
         [PatientStatus] VARCHAR(100) '$."PatientStatus"'
        ,[FolderName] VARCHAR(100) '$."FolderName"'
        ,[FolderOID] VARCHAR(100) '$."FolderOID"'
        ,[WindowReferenceFrom] VARCHAR(100) '$."WindowReferenceFrom"'
        ,[SVYN] VARCHAR(100) '$."SVYN"'
        ,[VisitEntered] VARCHAR(100) '$."VisitEntered"'
        ,[VisitDate] DATE '$."VisitDate"'
        ,[ProjectedVisitDate] DATE '$."ProjectedVisitDate"'
        ,[ProjectedStartWinDate] DATE '$."ProjectedStartWinDate"'
        ,[ProjectedEndWinDate] DATE '$."ProjectedEndWinDate"'
        ,[ProjectedOverDueDate] DATE '$."ProjectedOverDueDate"'
        
        ,[ScreenedDateString] VARCHAR(100) '$."ScreenedDateString"'
        ,[ScreenFailedDateString] VARCHAR(100) '$."ScreenFailedDateString"'
        ,[EnrolledDateString] VARCHAR(100) '$."EnrolledDateString"'
        ,[EarlyTerminatedDateString] VARCHAR(100) '$."EarlyTerminatedDateString"'
        ,[CompletedDateString] VARCHAR(100) '$."CompletedDateString"'
        
    )
WHERE StudyId=@StudyId AND RecordStatusId=1 AND DataElementCode = 'FULLVISITSCHEDULE'
)

-- Test the column availability
SELECT TOP 5
    PatientCode,
    PatientStatus,
    FolderOID,
    VisitDate_,      -- Formatted string column
    VisitDate,       -- Raw date column
    ProjectedVisitDate_, -- Formatted string column  
    ProjectedVisitDate   -- Raw date column
FROM FullVisitSchedule AS FVS
WHERE PatientCode = '350101'

-- This should show us what columns are actually available
-- and help identify the source of the "VisitDate_ not found" error

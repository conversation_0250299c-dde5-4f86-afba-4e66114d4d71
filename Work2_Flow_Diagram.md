# Work2.sql Data Flow Diagram

This diagram shows the flow of the SQL query that retrieves data element information with latest ingest run details.

```mermaid
graph TD
    A["🏁 START<br/>@StudyId = 1209<br/>SET NOCOUNT ON"] --> B["📋 DECLARE Table Variable<br/>@lastRunPerDE<br/>Store latest run per DataElement"]
    
    B --> C["📊 INSERT INTO @lastRunPerDE<br/>FROM Rrbm.StudyMetrics<br/>WHERE StudyId = 1220"]
    
    C --> D["🔍 GROUP BY DataElementCode<br/>MAX(IngestRunId) AS LastRunId<br/>MAX(DataElementCategoryId)"]
    
    D --> E["🔗 MAIN SELECT QUERY<br/>Multiple JOIN Operations"]
    
    E --> F["📋 JOIN 1: StudyDataElementCategories<br/>sdec.StudyId = 1220<br/>Base table for filtering"]
    
    F --> G["📊 JOIN 2: DataElements<br/>de.StudyDataElementCategoryId = sdec.Id<br/>Get element details"]
    
    G --> H["📈 JOIN 3: StudyMetrics<br/>CHECKSUM match + StudyId = 1220<br/>Link metrics data"]
    
    H --> I["👤 JOIN 4: People (Creator)<br/>p.Id = de.CreatedPersonId<br/>Get creator information"]
    
    I --> J["👤 JOIN 5: People (Modifier)<br/>p2.Id = de.ModifiedPersonId<br/>Get modifier information"]
    
    J --> K["🏃 JOIN 6: IngestRuns<br/>ir.Id = sm.IngestRunId<br/>Get run information"]
    
    K --> L["🔗 JOIN 7: @lastRunPerDE<br/>CHECKSUM match + LastRunId<br/>Filter to latest runs only"]
    
    L --> M["📋 JOIN 8: IngestDefinitions<br/>id.Id = ir.IngestDefinitionId<br/>Get definition names"]
    
    M --> N["📊 SELECT Columns<br/>Code, Display, DataType<br/>IngestDefinitionId, Name, CategoryId"]
    
    N --> O["🎨 FORMAT Dates<br/>CONCAT_WS for created/modified<br/>MMM-yy format + FirstName"]
    
    O --> P["📋 GROUP BY<br/>All non-aggregate columns<br/>Remove duplicates"]
    
    P --> Q["🏁 FINAL OUTPUT<br/>Data Elements with<br/>Latest Ingest Information"]
    
    %% Data Source Tables
    DS1[("🗄️ StudyDataElementCategories<br/>sdec")] --> F
    DS2[("🗄️ DataElements<br/>de")] --> G
    DS3[("🗄️ StudyMetrics<br/>sm")] --> H
    DS3 --> C
    DS4[("🗄️ People<br/>p (Creator)")] --> I
    DS5[("🗄️ People<br/>p2 (Modifier)")] --> J
    DS6[("🗄️ IngestRuns<br/>ir")] --> K
    DS7[("🗄️ IngestDefinitions<br/>id")] --> M
    
    %% Styling
    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style Q fill:#e8f5e8
    style C fill:#fff3e0
    style DS1 fill:#fce4ec
    style DS2 fill:#fce4ec
    style DS3 fill:#fce4ec
    style DS4 fill:#fce4ec
    style DS5 fill:#fce4ec
    style DS6 fill:#fce4ec
    style DS7 fill:#fce4ec
```

## Detailed Process Description

### 🚀 **Initialization Phase**
- **Variable Declaration**: Set Study ID to 1209 (but note: query uses 1220 in WHERE clauses)
- **Performance Setting**: `SET NOCOUNT ON` to suppress row count messages
- **Table Variable**: Create `@lastRunPerDE` to store latest run information per data element

### 📊 **Data Preparation (Steps 1-3)**
1. **Populate Table Variable**: Extract latest ingest run data from `StudyMetrics`
2. **Aggregation**: Use `MAX()` functions to get most recent run and category IDs
3. **Grouping**: Group by `DataElementCode` to ensure one record per element

### 🔗 **Main Query Join Sequence (Steps 4-11)**

#### **Primary Joins**:
1. **StudyDataElementCategories** → **DataElements**: Get element definitions for study
2. **DataElements** → **StudyMetrics**: Link elements to their metric data
3. **DataElements** → **People (2x)**: Get creator and modifier information
4. **StudyMetrics** → **IngestRuns**: Get run execution details
5. **StudyMetrics** → **@lastRunPerDE**: Filter to only latest runs
6. **IngestRuns** → **IngestDefinitions**: Get definition names

### 🎯 **Key Features**

#### **CHECKSUM Usage**:
- Used for efficient string comparison between `DataElementCode` and `de.Code`
- Helps performance on large datasets

#### **Date Formatting**:
- `FORMAT(date, 'MMM-yy')` creates readable month-year format
- `CONCAT_WS(', ', date, name)` combines date and person name

#### **Study ID Inconsistency**:
- ⚠️ **Variable**: `@StudyId = 1209`
- ⚠️ **Query**: Uses hardcoded `1220` in WHERE clauses

### 📋 **Output Columns**
- **de.Code**: Data element identifier
- **de.Display**: Human-readable element name
- **de.DataType**: Type of data stored
- **ir.ingestDefinitionId**: Reference to ingest definition
- **id.Name**: Ingest definition name
- **sm2.DataElementCategoryId**: Category classification
- **created**: Creation date and creator name
- **modified**: Modification date and modifier name

### 🎯 **Query Purpose**
This query creates a comprehensive view of data elements showing:
- Element metadata (code, display, type)
- Ingest processing information (definition, category)
- Audit trail (who created/modified and when)
- Only includes elements with the most recent ingest run data

### ⚠️ **Potential Issues**
1. **Study ID Mismatch**: Variable (1209) vs. hardcoded values (1220)
2. **CHECKSUM Collisions**: Rare but possible hash collisions
3. **Performance**: Multiple joins on large tables may be slow without proper indexing

### 🚀 **Recommendations**
1. **Fix Study ID**: Use `@StudyId` variable consistently
2. **Add Indexes**: On `StudyId`, `DataElementCode`, and join columns
3. **Consider Parameterization**: Make Study ID a parameter instead of hardcoded

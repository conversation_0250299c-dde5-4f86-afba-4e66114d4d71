-- Test script to verify discontinued patient filtering shows visits before discontinuation
-- This demonstrates the improved logic for discontinued patients

DECLARE @StudyId INT = 851;
DECLARE @DATEFORMAT VARCHAR(25) = 'yyyy-MM-dd';
DECLARE @TodaysDate DATE = GETDATE();

-- Example test data for a discontinued patient
-- Assume patient discontinued on 2024-12-15
-- Should show visits projected/completed before 2024-12-15
-- Should NOT show visits projected after 2024-12-15

WITH TestData AS (
    SELECT 
        '350102' AS PatientCode,
        'Discontinued' AS PatientStatus,
        '2024-12-15' AS EarlyTerminatedDateString,
        -- Test visits with different scenarios
        CASE 
            WHEN VisitType = 'CompletedBefore' THEN '2024-12-10'  -- Completed before discontinuation
            WHEN VisitType = 'ProjectedBefore' THEN NULL          -- Projected before discontinuation  
            WHEN VisitType = 'ProjectedAfter' THEN NULL           -- Projected after discontinuation
        END AS VisitDate,
        CASE 
            WHEN VisitType = 'CompletedBefore' THEN '2024-12-10'
            WHEN VisitType = 'ProjectedBefore' THEN '2024-12-12'  -- Before discontinuation
            WHEN VisitType = 'ProjectedAfter' THEN '2024-12-20'   -- After discontinuation
        END AS ProjectedVisitDate,
        VisitType + '_Visit' AS FolderName,
        VisitType AS FolderOID
    FROM (
        SELECT 'CompletedBefore' AS VisitType
        UNION ALL SELECT 'ProjectedBefore'
        UNION ALL SELECT 'ProjectedAfter'
    ) AS Visits
)

-- Test the filtering logic
SELECT 
    'DISCONTINUED PATIENT FILTERING TEST' AS TestType,
    PatientCode,
    PatientStatus,
    FolderName,
    VisitDate,
    ProjectedVisitDate,
    EarlyTerminatedDateString,
    CASE 
        WHEN (
            -- Apply the same logic as in work2.sql for discontinued patients
            PatientStatus = 'Discontinued' AND
            (VisitDate IS NOT NULL OR  -- Include all completed visits
             CAST(ProjectedVisitDate AS DATE) <= COALESCE(CAST(EarlyTerminatedDateString AS DATE), @TodaysDate))
        ) THEN 'INCLUDED'
        ELSE 'EXCLUDED'
    END AS FilterResult,
    CASE 
        WHEN VisitDate IS NOT NULL THEN 'Completed visit - should be included'
        WHEN CAST(ProjectedVisitDate AS DATE) <= CAST(EarlyTerminatedDateString AS DATE) THEN 'Projected before discontinuation - should be included'
        WHEN CAST(ProjectedVisitDate AS DATE) > CAST(EarlyTerminatedDateString AS DATE) THEN 'Projected after discontinuation - should be excluded'
    END AS ExpectedBehavior
FROM TestData
ORDER BY ProjectedVisitDate;

-- Expected Results:
-- CompletedBefore_Visit: INCLUDED (completed visit)
-- ProjectedBefore_Visit: INCLUDED (projected before discontinuation)  
-- ProjectedAfter_Visit: EXCLUDED (projected after discontinuation)

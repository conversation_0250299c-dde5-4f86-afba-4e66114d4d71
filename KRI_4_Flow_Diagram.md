# KRI_4 (ScreenFailRatioSite) Data Flow Diagram

This diagram shows how `KRI_4` flows through the SQL query from declaration to final output.

```mermaid
graph TD
    A["Variable Declaration<br/>@KRI_4 = 'ScreenFailRatioSite'"] --> B["AddMetricsSiteLevelAvg CTE<br/>Line 111: Filter criteria"]
    
    B --> C["StudyMetrics Table<br/>DataElementCode IN KRI_4<br/>Gets site-level averages"]
    
    C --> D["AddMetricsStudyLevelAvgStd CTE<br/>Calculates study-level stats"]
    
    D --> E["AddMetricsStudyLevelTresholds CTE<br/>Calculates thresholds"]
    
    A --> F["AddMetricsFieldsFromScoring CTE<br/>Gets scoring results from JSON"]
    
    F --> G["AllMetricsData CTE<br/>Joins scoring with metrics"]
    
    G --> H["AllMetricsDataInterpretation CTE<br/>Adds interpretation symbols"]
    
    E --> I["StudyData CTE<br/>Converts to string format"]
    
    I --> J["StudyData2 CTE<br/>Unpivots study-level data"]
    
    J --> K["StudyData3 CTE<br/>Line 274-276: OUTER APPLY for ScreenFailRatioSite"]
    
    H --> L["SiteLevelMainTable CTE<br/>Line 369-372: OUTER APPLY for site data"]
    
    L --> M["Site-level Output<br/>ScreenFailRatioSiteSymbol<br/>ScreenFailRatioSiteCharacter"]
    
    K --> N["StudyLevelMainTable CTE<br/>Line 443-444: Study-level columns"]
    
    N --> O["Study-level Output<br/>ScreenFailRatioSiteSymbol<br/>ScreenFailRatioSiteCharacter"]
    
    M --> P["Final UNION<br/>MainTable CTE"]
    N --> P
    
    P --> Q["Final SELECT<br/>Ordered by MetricType, SiteCode"]
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style F fill:#f3e5f5
    style M fill:#e8f5e8
    style O fill:#e8f5e8
    style Q fill:#fff3e0
```

## Detailed Flow Description

### 1. **Variable Declaration (Line 13)**
```sql
DECLARE @KRI_4 VARCHAR(100) = 'ScreenFailRatioSite';
```
- Defines the KRI identifier for Screen Failure Ratio metric

### 2. **Data Sources**
The `KRI_4` variable is used to filter data from two main sources:

#### A. **StudyMetrics Table** (via AddMetricsSiteLevelAvg CTE)
- **Line 111**: Used in WHERE clause to filter `DataElementCode`
- Calculates site-level averages for the current study month
- Generates study-level statistics (mean, standard deviation, thresholds)

#### B. **SiteAlgorithmRunResults Table** (via AddMetricsFieldsFromScoring CTE)
- Gets scoring algorithm results from JSON data
- Extracts color-coded interpretations (Green, Yellow, Red, etc.)

### 3. **Data Processing Steps**

#### **Site-Level Processing:**
1. **AddMetricsSiteLevelAvg**: Calculates site averages for ScreenFailRatioSite
2. **AddMetricsStudyLevelAvgStd**: Adds study-level averages and standard deviations
3. **AddMetricsStudyLevelTresholds**: Calculates threshold boundaries
4. **AllMetricsData**: Joins scoring data with metrics data
5. **AllMetricsDataInterpretation**: Adds symbols and characters for display

#### **Study-Level Processing:**
1. **StudyData**: Converts study statistics to string format
2. **StudyData2**: Unpivots the data for easier manipulation
3. **StudyData3**: Creates columns for each KRI metric including ScreenFailRatioSite

### 4. **Final Output Generation**

#### **SiteLevelMainTable CTE (Lines 369-372)**
```sql
OUTER APPLY (
    SELECT  [ScreenFailRatioSiteSymbol] = [sf].[InterpretedValueWithSymbol],
            [ScreenFailRatioSiteCharacter] = [sf].[InterpretedValueWithCharacter]
    FROM AllMetricsDataInterpretation [sf]
    WHERE [sd].[SiteCode] = [sf].[SiteCode] AND [sf].[MetricName] = @KRI_4
) [sf]
```

#### **StudyLevelMainTable CTE (Lines 443-444)**
```sql
[ScreenFailRatioSiteSymbol] = [ScreenFailRatioSite],
[ScreenFailRatioSiteCharacter] = [ScreenFailRatioSite],
```

### 5. **Final Result**
- **Site-level rows**: Show ScreenFailRatioSite values with color symbols/characters for each site
- **Study-level rows**: Show study averages, standard deviations, and thresholds
- Combined via UNION ALL and ordered by MetricType and SiteCode

## Output Columns Related to KRI_4
- `ScreenFailRatioSiteSymbol`: Value with emoji symbols (🟢🟡🔴)
- `ScreenFailRatioSiteCharacter`: Value with text characters ([G][A][R])

## Key Tables Used
- `[Rrbm].[StudyMetrics]`: Source of raw metric data
- `[Rrbm].[SiteAlgorithmRunResults]`: Source of scoring algorithm results
- `[Rrbm].[SiteSummaries]`: Site code mapping

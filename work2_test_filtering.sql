-- Test script to verify patient status filtering works correctly
-- Run this to see the difference before and after filtering

DECLARE @StudyId INT = 851;
DECLARE @DATEFORMAT VARCHAR(25) = 'yyyy-MM-dd';
DECLARE @TodaysDate DATE = GETDATE();

-- Test query to show filtering results
WITH FullVisitSchedule AS (
SELECT 
      [SiteCode]
    , [PatientCode]
    , [PatientStatus]
    , [ItemId]
    , [IntervalValue]    
    , [FolderName]
    , [FolderOID]
    , [WindowReferenceFrom]
    , [SVYN]
    , [VisitEntered]
    , [ProjectedStartWinDate] = FORMAT([ProjectedStartWinDate],@DATEFORMAT)
    , [VisitDate] =  FORMAT([VisitDate],@DATEFORMAT)
    , [ProjectedVisitDate] = FORMAT([ProjectedVisitDate],@DATEFORMAT)
    , [ProjectedEndWinDate] = FORMAT([ProjectedEndWinDate],@DATEFORMAT)
    , [ProjectedOverDueDate] = FORMAT([ProjectedOverDueDate],@DATEFORMAT)
    , [ScreenedDateString] 
    , [ScreenFailedDateString]
    , [EnrolledDateString]
    , [EarlyTerminatedDateString]
    , [CompletedDateString]

   FROM rrbm.StudyMetrics AS FullVisitSchedule 
    CROSS APPLY OPENJSON([FullVisitSchedule].[CustomProperties], '$.Properties') 
    WITH ( 
         [PatientStatus] VARCHAR(100) '$."PatientStatus"'
        ,[FolderName] VARCHAR(100) '$."FolderName"'
        ,[FolderOID] VARCHAR(100) '$."FolderOID"'
        ,[WindowReferenceFrom] VARCHAR(100) '$."WindowReferenceFrom"'
        ,[SVYN] VARCHAR(100) '$."SVYN"'
        ,[VisitEntered] VARCHAR(100) '$."VisitEntered"'
        ,[VisitDate] DATE '$."VisitDate"'
        ,[ProjectedVisitDate] DATE '$."ProjectedVisitDate"'
        ,[ProjectedStartWinDate] DATE '$."ProjectedStartWinDate"'
        ,[ProjectedEndWinDate] DATE '$."ProjectedEndWinDate"'
        ,[ProjectedOverDueDate] DATE '$."ProjectedOverDueDate"'
        
        ,[ScreenedDateString] VARCHAR(100) '$."ScreenedDateString"'
        ,[ScreenFailedDateString] VARCHAR(100) '$."ScreenFailedDateString"'
        ,[EnrolledDateString] VARCHAR(100) '$."EnrolledDateString"'
        ,[EarlyTerminatedDateString] VARCHAR(100) '$."EarlyTerminatedDateString"'
        ,[CompletedDateString] VARCHAR(100) '$."CompletedDateString"'
        
    )
WHERE StudyId=@StudyId AND RecordStatusId=1 AND DataElementCode = 'FULLVISITSCHEDULE'
)

-- Show comparison: Before and After filtering
SELECT 
    'BEFORE FILTERING' AS FilterStatus,
    PatientStatus,
    FolderOID,
    FolderName,
    COUNT(*) AS VisitCount
FROM FullVisitSchedule 
WHERE PatientCode = '350101'
GROUP BY PatientStatus, FolderOID, FolderName

UNION ALL

SELECT 
    'AFTER FILTERING' AS FilterStatus,
    PatientStatus,
    FolderOID,
    FolderName,
    COUNT(*) AS VisitCount
FROM FullVisitSchedule AS FVS
WHERE PatientCode = '350101'
  AND (
    -- Include all visits for enrolled/active patients
    (FVS.[PatientStatus] NOT IN ('Failed', 'ScreenFailed', 'Discontinued', 'Completed'))
    OR
    -- For screen-failed patients, ONLY include screening visits
    (FVS.[PatientStatus] IN ('Failed', 'ScreenFailed') AND FVS.[FolderOID] IN ('SCREEN', 'SCRN'))
    OR
    -- For discontinued patients, include visits up to discontinuation date
    (FVS.[PatientStatus] = 'Discontinued' AND 
     (FVS.[VisitDate] IS NOT NULL OR 
      FVS.[ProjectedVisitDate] <= COALESCE(FVS.[EarlyTerminatedDateString], CONVERT(VARCHAR(10), @TodaysDate, 120))))
    OR
    -- For completed patients, include all visits
    (FVS.[PatientStatus] = 'Completed')
  )
GROUP BY PatientStatus, FolderOID, FolderName
ORDER BY FilterStatus, FolderOID;

-- Expected result for patient 350101 (Failed status):
-- BEFORE FILTERING: ~33 visits (1 SCREEN + 32 other visits)
-- AFTER FILTERING: 1 visit (only SCREEN visit)

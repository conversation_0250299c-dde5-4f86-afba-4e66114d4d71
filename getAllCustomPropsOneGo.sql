DECLARE @StudyId INT = 584;
DECLARE @cols NVARCHAR(MAX), @sql NVARCHAR(MAX);
DECLARE @code NVARCHAR(100) = 'AETERM';

-- Get all distinct keys from the 'Properties' object across all JSON strings
SELECT @cols = STUFF((SELECT DISTINCT ',' + QUOTENAME(Properties.[key])
                      FROM Rrbm.StudyMetrics
                      CROSS APPLY OPENJSON(CustomProperties, '$.Properties') AS Properties
                      WHERE CustomProperties IS NOT NULL AND ISJSON(CustomProperties) = 1
                      AND StudyId = @StudyId
                      AND DataElementCode = @code -- Ensure this is set
                      AND RecordStatusId = 1
                      FOR XML PATH(''), TYPE
                     ).value('.', 'NVARCHAR(MAX)'), 1, 1, '');


IF @cols IS NULL
BEGIN
    SET @sql = N'
    SELECT
        Id,
        SiteCode,
        PatientCode,
        DataElementCode,
        Value,
        ValueNumeric,
        IntervalValue,
        IntervalStartDate,
        IntervalEndDate,
        CustomProperties
    FROM
        Rrbm.StudyMetrics
    WHERE StudyId = @StudyId
      AND DataElementCode = ''' + @code + ''' -- Ensure this filter is here
      AND RecordStatusId = 1;';
END
ELSE
BEGIN
    SET @sql = N'
    SELECT
        ydt.Id,
        ydt.SiteCode,
        ydt.PatientCode,
        ydt.DataElementCode,
        ydt.Value,
        ydt.ValueNumeric,
        ydt.IntervalValue,
        ydt.IntervalStartDate,
        ydt.IntervalEndDate,
        ' + @cols + '
    FROM
        Rrbm.StudyMetrics ydt
    LEFT JOIN
    (
        SELECT
            jd.Id,
            Properties.[key],
            Properties.value
        FROM
            Rrbm.StudyMetrics jd
        CROSS APPLY
            OPENJSON(jd.CustomProperties, ''$.Properties'') AS Properties
        WHERE jd.CustomProperties IS NOT NULL AND ISJSON(jd.CustomProperties) = 1
        AND jd.StudyId = @StudyId
        AND jd.DataElementCode = ''' + @code + '''
        AND jd.RecordStatusId = 1
    ) AS s
    PIVOT
    (
        MAX(value)
        FOR [key] IN (' + @cols + ')
    ) AS pvt ON ydt.Id = pvt.Id
    WHERE ydt.StudyId = @StudyId -- Apply filters to the outer query
      AND ydt.DataElementCode = ''' + @code + '''
      AND ydt.RecordStatusId = 1;';
END

EXEC sp_executesql @sql, N'@StudyId INT, @code NVARCHAR(100)', @StudyId = @StudyId, @code = @code;
# SQL Query Flow Diagram

```mermaid
flowchart TD
    A[Start] --> B[Declare @StudyId = 1125]
    B --> C[Declare @dataElement = 'CMTRT']
    C --> D[FROM Rrbm.StudyMetrics de]
    D --> E[LEFT JOIN Rrbm.Studies s<br/>ON de.StudyId = s.Id]
    E --> F{Apply WHERE conditions}
    F --> G[de.DataElementCode LIKE '%CMTRT%']
    G --> H[AND de.RecordStatusId = 1]
    H --> I[GROUP BY<br/>de.StudyId, s.Name, s.sponsor]
    I --> J["SELECT<br/>de.StudyId<br/>s.Name AS StudyName<br/>s.sponsor<br/>COUNT(*) AS Count"]
    J --> K[ORDER BY COUNT DESC]
    K --> L[Return Results]
    L --> M[End]

    style A fill:#e1f5fe
    style M fill:#e8f5e8
    style F fill:#fff3e0
    style I fill:#f3e5f5
```

## Flow Description

1. **Start**: Begin query execution
2. **Variable Declaration**: Set up parameters (@StudyId and @dataElement)
3. **Table Selection**: Start with Rrbm.StudyMetrics table
4. **JOIN Operation**: Left join with Rrbm.Studies table on StudyId
5. **Filtering**: Apply WHERE conditions to filter records
6. **Grouping**: Group results by StudyId, Name, and sponsor
7. **Selection**: Select required columns with COUNT aggregation
8. **Ordering**: Sort results by count in descending order
9. **Return Results**: Output final result set
10. **End**: Query execution complete

The flowchart uses different colors to highlight:
- **Blue**: Start point
- **Green**: End point  
- **Orange**: Decision/filtering logic
- **Purple**: Grouping operation

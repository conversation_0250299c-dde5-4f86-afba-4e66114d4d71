DECLARE @StudyId INT = 1125;
----------------------------------------------  
-- ----------------------------------------------------

DECLARE @lastRunPerDE AS TABLE (
    DataElementCode NVARCHAR(255),
    LastRunId INT
);

INSERT INTO @lastRunPerDE (DataElementCode, LastRunId)
SELECT DataElementCode,
    MAX(IngestRunId) AS LastRunId
FROM Rrbm.StudyMetrics
WHERE StudyId = @StudyId
GROUP BY DataElementCode;

SELECT de.Code,
    de.Display,
    de.DataType,
    ir.ingestDefinitionId,
    id.Name AS IngestDefinitionName,
    CONCAT_WS(
        ', ',
        FORMAT(de.CreatedDate, 'MMM-yy'),
        p.FirstName
    ) AS created,
    CONCAT_WS(
        ', ',
        FORMAT(de.ModifiedDate, 'MMM-yy'),
        p2.FirstName
    ) AS modified
FROM Rrbm.StudyDataElementCategories sdec
    JOIN Rrbm.DataElements de ON de.StudyDataElementCategoryId = sdec.Id
    JOIN Rrbm.StudyMetrics sm ON CHECKSUM(sm.DataElementCode) = CHECKSUM(de.Code)
    AND sm.StudyId = @StudyId
    JOIN Rrbm.People p ON p.Id = de.CreatedPersonId
    JOIN Rrbm.People p2 ON p2.Id = de.ModifiedPersonId
    JOIN Rrbm.IngestRuns ir ON ir.Id = sm.IngestRunId
    JOIN @lastRunPerDE sm2 ON CHECKSUM(sm2.DataElementCode) = CHECKSUM(de.Code)
    AND sm2.LastRunId = sm.IngestRunId
    JOIN Rrbm.IngestDefinitions id ON id.Id = ir.IngestDefinitionId

WHERE sdec.StudyId = @StudyId 
GROUP BY de.Code,
    de.Display,
    de.DataType,
    de.CreatedDate,
    de.ModifiedDate,
    p.FirstName,
    p2.FirstName,
    ir.ingestDefinitionId,
    id.Name 

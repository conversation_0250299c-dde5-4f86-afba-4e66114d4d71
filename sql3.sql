DECLARE @StudyId INT = 851;
----------------------------------------------  

DECLARE @columns NVARCHAR(MAX);
DECLARE @sql NVARCHAR(MAX); 
IF OBJECT_ID('tempdb..#Visits') IS NOT NULL DROP TABLE #Visits;

SELECT 
    [SiteCode]
    , [PatientCode]
    , [FolderOID]
    , [VisitName]
    , [RecordedVisitDate]
    , [VisitWindow] = CONCAT_WS(' - ',[EarliestWindowDate],[LatestWindowDate])
    /*
        N'❌','00_EnteredAsNotDone')
        N'✖','01_NotExpectedFor')
        N'🟡','04_CompletedEarlyOOW')   
        N'🟢','05_CompletedInTime')
        N'🟠','06_CompletedLateOOW')
        N'🔴','07_CompletedFutureDate') 
        N'⚪','14_UnreportedDue','15_UnreportedExpected')
        N'🚩','16_UnreportedOverdue')
        N'⏪⏩','80_FutureVisit')    
    */
    
    , [VisitStatus] = CASE LEFT([VisitStatus],2) 
                        WHEN 00 THEN N'❌'
                        WHEN 01 THEN N'✖'
                        WHEN 04 THEN CONCAT_WS(' ',[RecordedVisitDate],N'🟡')
                        WHEN 05 THEN CONCAT_WS(' ',[RecordedVisitDate],N'🟢')
                        WHEN 06 THEN CONCAT_WS(' ',[RecordedVisitDate],N'🟠')
                        WHEN 07 THEN CONCAT_WS(' ',[RecordedVisitDate],N'🔴')
                        WHEN 14 THEN CONCAT_WS(' ',[EarliestWindowDate],N'⚪',[LatestWindowDate]) 
                        WHEN 15 THEN CONCAT_WS(' ',[EarliestWindowDate],N'⚪',[LatestWindowDate]) 
                        WHEN 16 THEN CONCAT_WS(' ',[EarliestWindowDate],N'🚩',[LatestWindowDate]) 
                        WHEN 80 THEN CONCAT_WS(' ',[EarliestWindowDate],N'⏪⏩',[LatestWindowDate]) 
                    ELSE 
                        [VisitStatus]
                    END
    ,[CMReview] = CASE WHEN [CMReview] IN ('Not Done') THEN 1 ELSE 0 END 
    ,[SDReview] = CASE WHEN SDReview IN ('Not Done') THEN 1 ELSE 0 END
    ,[PatientStatus] 
    ,[ScreenedDateString]
    ,[ScreenFailedDateString]
    ,[EnrolledDateString] 
    ,[EarlyTerminatedDateString] 
    ,[CompletedDateString]
INTO #Visits
FROM rrbm.StudyMetrics AS [OOWRefVisBased] 
    CROSS APPLY OPENJSON([OOWRefVisBased].[CustomProperties], '$.Properties') 
    WITH (          
            [FolderOID] VARCHAR(100) '$."FolderOID"'
        ,[VisitName] VARCHAR(100) '$."VisitName"'
        ,[EarliestWindowDate] VARCHAR(100) '$."EarliestWindowDate"'
        ,[LatestWindowDate] VARCHAR(100) '$."LatestWindowDate"'
        ,[RecordedVisitDate] VARCHAR(100) '$."RecordedVisitDate"'
        ,[VisitStatus] VARCHAR(100) '$."VisitStatus"'
        
        ,[CMReview] VARCHAR(100) '$."CMReview"'            
        ,[SDReview] VARCHAR(100) '$."SDReview"'
        
        ,[PatientStatus] VARCHAR(100) '$."PatientStatus"'
        
        ,[ScreenedDateString] VARCHAR(100) '$."ScreenedDateString"'
        ,[ScreenFailedDateString] VARCHAR(100) '$."ScreenFailedDateString"'
        ,[EnrolledDateString] VARCHAR(100) '$."EnrolledDateString"'
        ,[EarlyTerminatedDateString] VARCHAR(100) '$."EarlyTerminatedDateString"'
        ,[CompletedDateString] VARCHAR(100) '$."CompletedDateString"'
        
    ) as [OOWV_Custom]

WHERE StudyId=@StudyId AND RecordStatusId=1 AND DataElementCode = 'ALLVISITSSTATUS'


/*
, PatientsList AS (
SELECT 
SiteCode, PatientCode, PatientStatus, ScreenedDateString, ScreenFailedDateString, EnrolledDateString,[EarlyTerminatedDateString], CompletedDateString 
FROM AllVisitDateData 
GROUP BY SiteCode, PatientCode, PatientStatus, ScreenedDateString, ScreenFailedDateString, EnrolledDateString, [EarlyTerminatedDateString], CompletedDateString 
)


SELECT * FROM PatientsList AS PatientsList


SELECT * FROM AllVisitDateData
*/

SELECT @columns = STRING_AGG(QUOTENAME([VisitName]), ',') 
FROM (SELECT DISTINCT [VisitName] FROM #Visits) AS VisitNames;

SET @sql = N'
SELECT 
    DISTINCT 
    SiteCode
    , PatientCode
    , PatientStatus 
    , ScreenedDateString, ScreenFailedDateString, EnrolledDateString,EarlyTerminatedDateString, CompletedDateString 
            , CMReviewPending 
            , SDReviewPending 
    ,' + @columns + N'

FROM 
(
    SELECT 
        SiteCode
        , PatientCode
        , PatientStatus           
        , VisitName
        , ScreenedDateString, ScreenFailedDateString, EnrolledDateString,EarlyTerminatedDateString, CompletedDateString 
        , CMReviewPending = SUM(CMReview) OVER (PARTITION BY PatientCode)
        , SDReviewPending = SUM(SDReview) OVER (PARTITION BY PatientCode)

        , VisitStatus

    FROM #Visits
) AS SourceTable
PIVOT
(
    MAX(VisitStatus) 
    FOR VisitName IN (' + @columns + N')
) AS PivotTable
ORDER BY SiteCode, PatientCode, PatientStatus
';

EXEC sp_executesql @sql;
/* Cleanup temporary table */
DROP TABLE #Visits;
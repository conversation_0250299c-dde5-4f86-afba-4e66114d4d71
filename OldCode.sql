DECLARE @StudyId INT = 851;
-- -- -- -------------06_FullVisitSchedule---------------------------------------
DECLARE @DATEFORMAT VARCHAR(25) = 'yyyy-MM-dd';
DECLARE @TodaysDate DATE = GETDATE();
WITH cte_SubjectDisposition AS (
	SELECT [PatientStatusTable].[SiteCode],
		[PatientStatusTable].[PatientCode],
		[SD_custom].[PatientStatus],
		[SD_custom].[ScreenedDate],
		[SD_custom].[EnrolledDate],
		[ScreenedDateString] = FORMAT([ScreenedDate], @DATEFORMAT),
		[EnrolledDateString] = FORMAT([EnrolledDate], @DATEFORMAT),
		[ScreenFailedDateString] = FORMAT([ScreenFailedDate], @DATEFORMAT),
		[EarlyTerminatedDateString] = FORMAT([EarlyTerminatedDate], @DATEFORMAT),
		[CompletedDateString] = FORMAT([CompletedDate], @DATEFORMAT)
	FROM rrbm.StudyMetrics AS [PatientStatusTable]
		CROSS APPLY OPENJSON(
			[PatientStatusTable].[CustomProperties],
			'$.Properties'
		) WITH (
			[PatientStatus] VARCHAR(100) '$."CustomPatientStatus"',
			[ScreenedDate] DATE '$."ScreenedDateString"',
			[ScreenFailedDate] DATE '$."ScreenFailedDateString"',
			[EnrolledDate] DATE '$."EnrolledDateString"',
			[EarlyTerminatedDate] DATE '$."EarlyTerminatedDateString"',
			[CompletedDate] DATE '$."CompletedDateString"'
		) AS [SD_custom]
	WHERE [PatientStatusTable].[StudyId] = @StudyId
		AND [PatientStatusTable].[RecordStatusId] = 1
		AND [PatientStatusTable].[DataElementCode] = 'SubjectStatus'
)
/* SELECT * FROM cte_SubjectDisposition */
,
cte_StudySchedule AS (
	SELECT [SS_custom].[Ordinal],
		[SS_custom].[FolderName],
		[SS_custom].[WindowReferenceFrom],
		[SS_custom].[FolderOID],
		[SS_custom].[StartWinDays],
		[SS_custom].[Targetdays],
		[SS_custom].[EndWinDays],
		[SS_custom].[OverDueDays],
		[SS_custom].[ReferenceTargetDays],
		[StudySchedule].[IntervalValue]
	FROM rrbm.StudyMetrics AS [StudySchedule]
		CROSS APPLY OPENJSON(
			[StudySchedule].[CustomProperties],
			'$.Properties'
		) WITH (
			[FolderName] VARCHAR(100) '$."FolderName"',
			[WindowReferenceFrom] VARCHAR(100) '$."WindowReferenceFrom"',
			[FolderOID] VARCHAR(100) '$."FolderOID"',
			[Ordinal] INT '$."Ordinal"',
			[StartWinDays] INT '$."StartWinDays"',
			[TargetDays] INT '$."TargetDays"',
			[EndWinDays] INT '$."EndWinDays"',
			[OverDueDays] INT '$."OverDueDays"',
			[ReferenceTargetDays] INT '$."ReferenceTargetDays"'
		) AS [SS_custom]
	WHERE [StudySchedule].[StudyId] = @StudyId
		AND [StudySchedule].[RecordStatusId] = 1
		AND [StudySchedule].[DataElementCode] = 'STUDYSCHEDULE' --AND [SS_custom].[WindowReferenceFrom] <> 'NA'
)
/* SELECT * FROM cte_StudySchedule --*/
,
cte_VisitDate AS (
	SELECT [PatientCode],
		[VisitEntered] = [Value],
		[VD_custom].[SVYN],
		[VisitDate] = [IntervalStartDate],
		[VD_custom].[FolderOID],
		[IntervalValue]
	FROM rrbm.StudyMetrics AS [DateOfVisitTable]
		CROSS APPLY OPENJSON(
			[DateOfVisitTable].[CustomProperties],
			'$.Properties'
		) WITH (
			[FolderOID] VARCHAR(100) '$."FolderOID"',
			[SVYN] VARCHAR(100) '$."SVYN"'
		) AS [VD_custom]
	WHERE [DateOfVisitTable].[StudyId] = @StudyId
		AND [DateOfVisitTable].[RecordStatusId] = 1
		AND [DateOfVisitTable].[DataElementCode] IN ('SVDAT')
)
/* SELECT * FROM cte_VisitDate --*/
,
FullSchedule AS (
	SELECT [ItemId] = CONCAT(
			[StudySchedule].[Ordinal],
			'_',
			ROW_NUMBER() OVER (
				PARTITION BY [PatientStatusTable].[PatientCode],
				[StudySchedule].[Ordinal]
				ORDER BY [PatientStatusTable].[PatientCode],
					[StudySchedule].[Ordinal]
			)
		),
		[PatientStatusTable].[SiteCode],
		[PatientStatusTable].[PatientCode],
		[PatientStatusTable].[PatientStatus],
		[PatientStatusTable].[ScreenedDate],
		[PatientStatusTable].[EnrolledDate],
		[PatientStatusTable].[ScreenedDateString],
		[PatientStatusTable].[EnrolledDateString],
		[PatientStatusTable].[ScreenFailedDateString],
		[PatientStatusTable].[EarlyTerminatedDateString],
		[PatientStatusTable].[CompletedDateString],
		[StudySchedule].[Ordinal],
		[StudySchedule].[WindowReferenceFrom],
		[StudySchedule].[FolderName],
		[StudySchedule].[FolderOID],
		[StudySchedule].[StartWinDays],
		[StudySchedule].[Targetdays],
		[StudySchedule].[EndWinDays],
		[StudySchedule].[OverDueDays],
		[StudySchedule].[ReferenceTargetDays],
		[IntervalValue] = [IV].[IntervalValue]
		/* It's better to have this defined in the Study Sched file, but it's not worth putting those in for the demo */
,
		[ReferenceVisitDate].[ReferenceVisitEntered],
		[ReferenceVisitDate].[ReferenceVisitDate],
		[DateOfVisitTable].[SVYN],
		[DateOfVisitTable].[VisitEntered],
		[DateOfVisitTable].[VisitDate],
		[SVFolderOID],
		[RefVFolderOID],
		[ProjectedVisitDate] = CASE
			WHEN [WindowReferenceFrom] = 'NA'
			OR [PatientStatusTable].[PatientStatus] IN ('InScreening', 'ScreenFailed') THEN [VisitDate]
			ELSE COALESCE (
				DATEADD(
					DAY,
					[StudySchedule].[ReferenceTargetDays],
					CAST(
						[ReferenceVisitDate].[ReferenceVisitDate] AS DATE
					)
				),
				DATEADD(
					DAY,
					[StudySchedule].[Targetdays],
					[PatientStatusTable].[EnrolledDate]
				),
				DATEADD(
					DAY,
					[StudySchedule].[Targetdays] + 8,
					[PatientStatusTable].[ScreenedDate]
				)
			)
		END
	FROM [cte_SubjectDisposition] AS [PatientStatusTable]
		OUTER APPLY (
			SELECT *
			FROM [cte_StudySchedule] AS [StudySchedule]
		) AS [StudySchedule]
		OUTER APPLY (
			SELECT [VisitEntered],
				[SVYN],
				[VisitDate],
				[SVFolderOID] = [FolderOID]
			FROM [cte_VisitDate] AS [DateOfVisitTable]
			WHERE [PatientStatusTable].[PatientCode] = [DateOfVisitTable].PatientCode
				AND [StudySchedule].[FolderOID] = [DateOfVisitTable].[FolderOID]
		) AS [DateOfVisitTable]
		OUTER APPLY (
			SELECT [ReferenceVisitEntered] = [VisitEntered],
				[ReferenceVisitDate] = [VisitDate],
				[RefVFolderOID] = [FolderOID]
			FROM [cte_VisitDate] AS [ReferenceVisitDate]
			WHERE [PatientStatusTable].[PatientCode] = [ReferenceVisitDate].PatientCode
				AND [StudySchedule].[WindowReferenceFrom] = [ReferenceVisitDate].[FolderOID]
		) AS [ReferenceVisitDate]
		OUTER APPLY (
			SELECT [FolderOID],
				[IntervalValue]
			FROM cte_VisitDate AS IV
			WHERE [StudySchedule].[FolderOID] = [IV].[FolderOID]
			GROUP BY [FolderOID],
				[IntervalValue]
		) AS [IV]
	WHERE NOT(
			[PatientStatusTable].[PatientStatus] IN ('ScreenFailed', 'Discontinued', 'Completed')
			AND [DateOfVisitTable].[VisitEntered] IS NULL
		)
)
SELECT [ProjectedStartWinDate_] = FORMAT(
		DATEADD(DAY,(-1 * StartWinDays), ProjectedVisitDate),
		@DATEFORMAT
	),
	[VisitDate_] = FORMAT(VisitDate, @DATEFORMAT),
	[ProjectedEndWinDate_] = FORMAT(
		DATEADD(DAY, EndWinDays, ProjectedVisitDate),
		@DATEFORMAT
	),
	[ProjectedVisitDate_] = FORMAT(ProjectedVisitDate, @DATEFORMAT),
	[ProjectedOverDueDate_] = FORMAT(
		DATEADD(DAY, OverDueDays, ProjectedVisitDate),
		@DATEFORMAT
	),
	*
FROM FullSchedule
WHERE COALESCE(VisitDate, ProjectedVisitDate) IS NOT NULL
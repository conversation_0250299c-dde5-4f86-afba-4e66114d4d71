DECLARE @StudyId int = 1220;
-- ----------------------------------------------------
DECLARE @EnrolledSubjects TABLE (
    SiteCode NVARCHAR(50),
    PatientCode NVARCHAR(50),
    RandDate DATE
    ) -- ----------------------------------------------------
INSERT INTO @EnrolledSubjects (SiteCode, PatientCode, RandDate)
SELECT DISTINCT SiteCode,
    PatientCode,
    IntervalStartDate
FROM Rrbm.StudyMetrics
WHERE StudyId = @StudyId
    AND DataElementCode = 'EnrollmentStartDate'
    AND RecordStatusId = 1
    AND IntervalStartDate IS NOT NULL;
-- SELECT * FROM @EnrolledSubjects;
-----------------------------------------------------------------
SELECT *,
    rowId = ROW_NUMBER() OVER (ORDER BY [randMonth], [SiteCode])
FROM (
    SELECT [randMonth],
    [SiteCode],
    [ageGrp],
    [w26cumulative],
    [w29cumulative],
    w26Pct = cast(
        w26cumulative * 1.0 / NULLIF(w26cumulative + w29cumulative, 0) * 100 as decimal(5, 2)
    ),
    w29Pct = cast(
        w29cumulative * 1.0 / NULLIF(w26cumulative + w29cumulative, 0) * 100 as decimal(5, 2)
    )
    FROM (
    SELECT [randMonth],
        [SiteCode],
        [ageGrp],
        w26cumulative = SUM(w26sum) OVER (
           
        ORDER BY [randMonth] ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ),
        w29cumulative = SUM(w29sum) OVER (
           
        ORDER BY [randMonth] ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        )
    FROM (
        SELECT [randMonth],
        [SiteCode],
        [ageGrp],
        w26sum = SUM(w26above) OVER (
            PARTITION BY [randMonth], [ageGrp], [SiteCode]
            ORDER BY [randMonth] ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ),
        w29sum = SUM(w29above) OVER (
            PARTITION BY [randMonth], [ageGrp], [SiteCode]
            ORDER BY [randMonth] ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        )
        FROM (
        SELECT [SiteCode],
            [PatientCode],
            [RandDate],
            [randMonth],
            [ageGrp],
            w26above = CASE
            WHEN ageGrp = '26 weeks+0 days to 28 weeks + 6' THEN 1
            ELSE 0
            END,
            w29above = CASE
            WHEN ageGrp = '29 weeks+0 days to 31 weeks + 6' THEN 1
            ELSE 0
            END
        FROM (
            SELECT DISTINCT [a].[SiteCode],
            [a].[PatientCode],
            [es].[RandDate],
            randMonth = DATEFROMPARTS(YEAR(es.RandDate), MONTH(es.RandDate), 1),
            ageGrp = CASE
                WHEN WeeksLMP BETWEEN 26 and 28
                AND DaysLMP BETWEEN 0 AND 6 THEN '26 weeks+0 days to 28 weeks + 6'
                WHEN WeeksLMP BETWEEN 29 and 31
                AND DaysLMP BETWEEN 0 AND 6 THEN '29 weeks+0 days to 31 weeks + 6'
                ELSE 'Other Gestational Ages'
            END
            FROM (
            SELECT SiteCode,
                dm.PatientCode,
                dm.[Value],
                LMPmethod = JSON_VALUE(CustomProperties, '$.Properties.LMPmethod'),
                WeeksLMP = JSON_VALUE(CustomProperties, '$.Properties.WeeksLMP'),
                DaysLMP = JSON_VALUE(CustomProperties, '$.Properties.DaysLMP'),
                WeeksUS = JSON_VALUE(CustomProperties, '$.Properties.WeeksUS'),
                DaysUS = JSON_VALUE(CustomProperties, '$.Properties.DaysUS')
            FROM Rrbm.StudyMetrics dm
            WHERE dm.StudyId = @StudyId
                and dm.DataElementCode = 'GestationalAge'
                and dm.RecordStatusId = 1
            ) a
            INNER JOIN @EnrolledSubjects es ON es.PatientCode = a.PatientCode
        ) b
        ) c
    ) d
    WHERE ageGrp IS NOT NULL
    ) e
) f
WHERE w26Pct IS NOT NULL
    AND w29Pct IS NOT NULL
GROUP BY [randMonth],
    [SiteCode],
    [ageGrp],
    w26cumulative,
    w29cumulative,
    w26Pct,
    w29Pct

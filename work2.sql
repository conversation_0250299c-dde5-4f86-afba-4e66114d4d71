DECLARE @StudyId INT = 851;
---------- 07_AllVisitsStatus ----------
DECLARE @DATEFORMAT VARCHAR(25) = 'yyyy-MM-dd';
DECLARE @TodaysDate DATE = GETDATE();
WITH FullVisitSchedule AS (
  SELECT [SiteCode],
    [PatientCode],
    [PatientStatus],
    [ItemId],
    [IntervalValue],
    [VisitStatus] = CASE
      WHEN [VisitDate] IS NULL THEN CASE
        WHEN [ProjectedVisitDate] IS NOT NULL THEN CASE
          WHEN [SVYN] = 'No' THEN '00_EnteredAsNotDone'
          WHEN @TodaysDate >= [ProjectedStartWinDate]
          AND [ProjectedVisitDate] >= @TodaysDate THEN '15_UnreportedExpected'
          WHEN @TodaysDate > [ProjectedVisitDate]
          AND @TodaysDate <= [ProjectedEndWinDate] THEN '14_UnreportedDue'
          WHEN @TodaysDate > [ProjectedEndWinDate] THEN '16_UnreportedOverdue'
          WHEN @TodaysDate < [ProjectedStartWinDate] THEN '80_FutureVisit'
          ELSE CONCAT(
            '92_Label Error - SVYN = ',
            [SVYN],
            ', VisitDate = ',
            [VisitDate],
            ', Visit Window = ',
            FORMAT([ProjectedStartWinDate], @DATEFORMAT),
            '<<',
            [ProjectedVisitDate],
            '>>',
            FORMAT([ProjectedEndWinDate], @DATEFORMAT),
            ' - Investigate'
          )
        END
        ELSE CONCAT(
          '91_Label Error - SVYN = ',
          [SVYN],
          ', VisitDate = ',
          [VisitDate],
          ', Visit Window = ',
          FORMAT([ProjectedStartWinDate], @DATEFORMAT),
          '<<',
          [ProjectedVisitDate],
          '>>',
          FORMAT([ProjectedEndWinDate], @DATEFORMAT),
          ' - Investigate'
        )
      END
      WHEN [VisitDate] IS NOT NULL THEN CASE
        WHEN [ProjectedVisitDate] IS NOT NULL THEN CASE
          WHEN (
            [VisitDate] >= [ProjectedStartWinDate]
            AND [VisitDate] <= [ProjectedEndWinDate]
          )
          OR [VisitDate] = [ProjectedVisitDate] THEN '05_CompletedInTime'
          WHEN [VisitDate] < [ProjectedStartWinDate] THEN '04_CompletedEarlyOOW'
          WHEN [VisitDate] > [ProjectedEndWinDate] THEN '06_CompletedLateOOW'
          /* WHEN [ProjectedStartWinDate] <= @TodaysDate AND [ProjectedVisitDate] >= @TodaysDate THEN '15_Expected' */
          ELSE CONCAT(
            '92_Label Error - SVYN = ',
            [SVYN],
            ', VisitDate = ',
            [VisitDate],
            ', Visit Window = ',
            FORMAT([ProjectedStartWinDate], @DATEFORMAT),
            '<<',
            [ProjectedVisitDate],
            '>>',
            FORMAT([ProjectedEndWinDate], @DATEFORMAT),
            ' - Investigate'
          )
        END
        WHEN [ProjectedVisitDate] IS NULL THEN CASE
          WHEN [VisitEntered] IS NOT NULL
          AND [FolderOID] = 'SCREEN' THEN '05_CompletedInTime'
          ELSE CONCAT(
            '92_Label Error - SVYN = ',
            [SVYN],
            ', VisitDate = ',
            [VisitDate],
            ', Visit Window = ',
            FORMAT([ProjectedStartWinDate], @DATEFORMAT),
            '<<',
            [ProjectedVisitDate],
            '>>',
            FORMAT([ProjectedEndWinDate], @DATEFORMAT),
            ' - Investigate'
          )
        END
        ELSE CONCAT(
          '91_Label Error - SVYN = ',
          [SVYN],
          ', VisitDate = ',
          [VisitDate],
          ', Visit Window = ',
          FORMAT([ProjectedStartWinDate], @DATEFORMAT),
          '<<',
          [ProjectedVisitDate],
          '>>',
          FORMAT([ProjectedEndWinDate], @DATEFORMAT),
          ' - Investigate'
        )
      END
      ELSE CONCAT(
        '90_Label Error - SVYN = ',
        [SVYN],
        ', VisitDate = ',
        [VisitDate],
        ', Visit Window = ',
        FORMAT([ProjectedStartWinDate], @DATEFORMAT),
        '<<',
        [ProjectedVisitDate],
        '>>',
        FORMAT([ProjectedEndWinDate], @DATEFORMAT),
        ' - Investigate'
      )
    END,
    [FolderName],
    [FolderOID],
    [WindowReferenceFrom],
    [SVYN],
    [VisitEntered],
    [ProjectedStartWinDate] = FORMAT([ProjectedStartWinDate], @DATEFORMAT),
    [VisitDate] = FORMAT([VisitDate], @DATEFORMAT),
    [ProjectedVisitDate] = FORMAT([ProjectedVisitDate], @DATEFORMAT),
    [ProjectedEndWinDate] = FORMAT([ProjectedEndWinDate], @DATEFORMAT),
    [ProjectedOverDueDate] = FORMAT([ProjectedOverDueDate], @DATEFORMAT),
    [ScreenedDateString],
    [ScreenFailedDateString],
    [EnrolledDateString],
    [EarlyTerminatedDateString],
    [CompletedDateString]
    /*
     , [Today] = @TodaysDate
     , *
     */
  FROM rrbm.StudyMetrics AS FullVisitSchedule
    CROSS APPLY OPENJSON(
      [FullVisitSchedule].[CustomProperties],
      '$.Properties'
    ) WITH (
      [PatientStatus] VARCHAR(100) '$."PatientStatus"',
      [FolderName] VARCHAR(100) '$."FolderName"',
      [FolderOID] VARCHAR(100) '$."FolderOID"',
      [WindowReferenceFrom] VARCHAR(100) '$."WindowReferenceFrom"',
      [SVYN] VARCHAR(100) '$."SVYN"',
      [VisitEntered] VARCHAR(100) '$."VisitEntered"',
      [VisitDate] DATE '$."VisitDate"',
      [ProjectedVisitDate] DATE '$."ProjectedVisitDate"',
      [ProjectedStartWinDate] DATE '$."ProjectedStartWinDate"',
      [ProjectedEndWinDate] DATE '$."ProjectedEndWinDate"',
      [ProjectedOverDueDate] DATE '$."ProjectedOverDueDate"',
      [ScreenedDateString] VARCHAR(100) '$."ScreenedDateString"',
      [ScreenFailedDateString] VARCHAR(100) '$."ScreenFailedDateString"',
      [EnrolledDateString] VARCHAR(100) '$."EnrolledDateString"',
      [EarlyTerminatedDateString] VARCHAR(100) '$."EarlyTerminatedDateString"',
      [CompletedDateString] VARCHAR(100) '$."CompletedDateString"'
    )
  WHERE StudyId = @StudyId
    AND RecordStatusId = 1
    AND DataElementCode = 'FULLVISITSCHEDULE'
)
SELECT VisitStatus_ = CASE
    VisitStatus
    WHEN '00_EnteredAsNotDone' THEN 'EnteredAsNotDone'
    WHEN '04_CompletedEarlyOOW' THEN 'Completed OOW'
    WHEN '05_CompletedInTime' THEN 'Completed In Time'
    WHEN '06_CompletedLateOOW' THEN 'Completed OOW'
    WHEN '14_UnreportedDue' THEN 'Unreported - still in window'
    WHEN '15_UnreportedExpected' THEN 'Unreported - still in window'
    WHEN '16_UnreportedOverdue' THEN 'Overdue'
    WHEN '80_FutureVisit' THEN 'Future Visit'
    ELSE RIGHT(VisitStatus, LEN(VisitStatus) -3)
  END,
  *
FROM FullVisitSchedule AS FVS
WHERE
  -- PATIENT STATUS FILTERING TO EXCLUDE INAPPROPRIATE VISITS
  -- =====================================================

  -- Include all visits for enrolled/active patients (InScreening, Enrolled, etc.)
  (FVS.[PatientStatus] NOT IN ('Failed', 'ScreenFailed', 'Discontinued', 'Completed'))
  OR
  -- For screen-failed patients, ONLY include screening visits (exclude all post-enrollment visits)
  (FVS.[PatientStatus] IN ('Failed', 'ScreenFailed') AND FVS.[FolderOID] IN ('SCREEN', 'SCRN'))
  OR
  -- For discontinued patients, include ALL visits before discontinuation date (completed OR projected)
  (FVS.[PatientStatus] = 'Discontinued' AND
   (FVS.[VisitDate] IS NOT NULL OR  -- Include all completed visits
    CAST(FVS.[ProjectedVisitDate] AS DATE) <= COALESCE(CAST(FVS.[EarlyTerminatedDateString] AS DATE), @TodaysDate)))  -- Include visits projected before discontinuation
  OR
  -- For completed patients, include all visits (they completed the full study)
  (FVS.[PatientStatus] = 'Completed')

-- This filtering prevents:
-- 1. Screen-failed patients from showing 30+ "overdue" visits they can never complete
-- 2. Discontinued patients from showing visits scheduled AFTER their discontinuation date
-- 3. False compliance alerts for impossible visits
--
-- For discontinued patients: Shows ALL visits (completed + projected) up to discontinuation date
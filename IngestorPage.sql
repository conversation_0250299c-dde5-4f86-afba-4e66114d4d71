-- Get min and max ModifiedDate for the study
DECLARE @StudyId INT = 1092;
WITH DateBounds AS (
    SELECT MinDate = MIN(CAST(ModifiedDate AS DATE)),
        MaxDate = MAX(CAST(ModifiedDate AS DATE))
    FROM [Rrbm].[IngestDefinitions]
    WHERE [StudyId] = @StudyId
        AND [StatusId] = 3 -- active
),
WeekIntervals AS (
    SELECT StartOfWeek = MinDate,
        EndOfWeek = DATEADD(DAY, 6, MinDate)
    FROM DateBounds
    UNION ALL
    SELECT StartOfWeek = DATEADD(DAY, 7, StartOfWeek),
        EndOfWeek = DATEADD(DAY, 13, StartOfWeek)
    FROM WeekIntervals,
        DateBounds
    WHERE DATEADD(DAY, 7, StartOfWeek) <= MaxDate
),
ingests AS (
    SELECT 
       id, ModifiedDate
    FROM [Rrbm].[IngestDefinitions]
    WHERE [StudyId] = @StudyId
        AND [StatusId] = 3 -- active
)

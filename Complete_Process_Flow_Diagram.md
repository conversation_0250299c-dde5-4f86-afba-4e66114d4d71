# Complete SQL Process Flow Diagram

This diagram shows the complete flow of the KRI (Key Risk Indicator) analysis SQL query from start to finish.

```mermaid
graph TD
    A["🏁 START<br/>Study ID: @StudyId<br/>Date Format Setup"] --> B["📋 Variable Declarations<br/>@KRI_1 to @KRI_11<br/>11 KRI Metrics"]
    
    B --> C["⚙️ STEP 1: ConfigurationId<br/>Get Active Algorithm Configuration<br/>EntityTypeId = 2 (Site Level)"]
    
    C --> D["🏃 STEP 2: AlgorithmRunId<br/>Get Latest Algorithm Run<br/>ROW_NUMBER by ExecutionDate DESC"]
    
    D --> E["📊 STEP 3: AddMetricsFieldsFromScoring<br/>Extract JSON Scoring Results<br/>Parse Colors & Symbols"]
    
    B --> F["📈 STEP 4: AddMetricsSiteLevelAvg<br/>Calculate Site Averages<br/>Filter by 11 KRI Metrics"]
    
    F --> G["📊 STEP 5: AddMetricsStudyLevelAvgStd<br/>Calculate Study Statistics<br/>AVG, STD for each KRI"]
    
    G --> H["📏 STEP 6: AddMetricsStudyLevelTresholds<br/>Calculate Thresholds<br/>±1 STD, ±2 STD boundaries"]
    
    E --> I["🔗 STEP 7: AllMetricsData<br/>JOIN Scoring + Metrics<br/>Combine Colors with Values"]
    H --> I
    
    I --> J["🎨 STEP 8: AllMetricsDataInterpretation<br/>Add Visual Elements<br/>Symbols 🟢🟡🔴 & Characters [G][A][R]"]
    
    J --> K["👥 STEP 9: SiteData<br/>Count Screened & Enrolled<br/>by Site"]
    
    H --> L["📋 STEP 10: StudyData<br/>Convert to String Format<br/>Prepare for UNPIVOT"]
    
    L --> M["🔄 STEP 11: StudyData2<br/>UNPIVOT Study Statistics<br/>6 Metric Types per KRI"]
    
    M --> N["📊 STEP 12: StudyData3<br/>Transform to Final Format<br/>11 KRI Columns with OUTER APPLY"]
    
    J --> O["🏢 STEP 13: SiteLevelMainTable<br/>Site-Level Results<br/>Join Site Data + Interpretations"]
    K --> O
    
    N --> P["📈 STEP 14: StudyLevelMainTable<br/>Study-Level Results<br/>Statistics & Thresholds"]
    
    O --> Q["🔄 STEP 15: MainTable<br/>UNION ALL<br/>Site + Study Results"]
    P --> Q
    
    Q --> R["📋 FINAL OUTPUT<br/>ORDER BY MetricType, SiteCode<br/>Complete KRI Dashboard"]
    
    %% Data Source Tables
    DS1[("🗄️ AlgorithmConfigurations<br/>Rrbm Schema")] --> C
    DS2[("🗄️ AlgorithmRuns<br/>Rrbm Schema")] --> D
    DS3[("🗄️ SiteAlgorithmRunResults<br/>JSON Data")] --> E
    DS4[("🗄️ SiteSummaries<br/>Site Codes")] --> E
    DS5[("🗄️ StudyMetrics<br/>Raw KRI Data")] --> F
    DS5 --> K
    
    %% Styling
    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style R fill:#e8f5e8
    style Q fill:#fff3e0
    style DS1 fill:#fce4ec
    style DS2 fill:#fce4ec
    style DS3 fill:#fce4ec
    style DS4 fill:#fce4ec
    style DS5 fill:#fce4ec
```

## Detailed Process Description

### 🚀 **Initialization Phase**
- **Variables**: Set up StudyId and 11 KRI metric identifiers
- **Date Format**: Configure output date formatting

### 📊 **Data Source Preparation (Steps 1-2)**
1. **ConfigurationId**: Find active scoring algorithm configuration
2. **AlgorithmRunId**: Get the most recent algorithm execution

### 🎯 **Dual Processing Paths**

#### **Path A: Scoring Data (Step 3)**
- Extract algorithm results from JSON
- Parse color-coded interpretations
- Convert to symbols and characters

#### **Path B: Metrics Data (Steps 4-6)**
- Calculate site-level averages for each KRI
- Compute study-level statistics (mean, std dev)
- Generate threshold boundaries (±1σ, ±2σ)

### 🔗 **Data Integration (Steps 7-8)**
- Join scoring results with statistical data
- Add visual interpretation elements
- Create comprehensive metric profiles

### 🏗️ **Output Preparation (Steps 9-12)**

#### **Site Data Processing**:
- Count screening and enrollment numbers
- Prepare site-level context

#### **Study Data Processing**:
- Convert statistics to string format
- Unpivot data for consistency
- Transform into final column structure

### 📊 **Final Assembly (Steps 13-15)**

#### **Two Result Sets**:
1. **Site-Level Table**: Individual site performance with colored indicators
2. **Study-Level Table**: Aggregate statistics and thresholds

#### **Final Union**: Combine both result sets and order for presentation

## 📋 **Output Structure**

### **Site-Level Rows** (MetricType = 'Site'):
- Site identification and counts
- 11 KRI metrics with color symbols/characters
- Individual site performance vs. study benchmarks

### **Study-Level Rows** (6 rows per execution):
- `StudyAvg`: Study-wide averages
- `Study1Std`: One standard deviation
- `TresholdMinus1Std`: Lower warning threshold
- `TresholdPlus1Std`: Upper warning threshold  
- `TresholdMinus2Std`: Lower critical threshold
- `TresholdPlus2Std`: Upper critical threshold

## 🎨 **Color Coding System**
- 🟢 **Green [G]**: Good performance
- 🟡 **Yellow [A]**: Amber/Warning
- 🔴 **Red [R]**: Critical/Risk
- ⚪ **Gray [-]**: No data available
- 🔷 **Blue [?]**: Scoring tie

## 📊 **Key Performance Indicators (KRIs)**
1. AE Incidence Rate
2. Data Latency Normalized Running
3. Enrollment Rate Site
4. Screen Failure Ratio Site
5. Discontinuation Ratio Site
6. Deviation Ratio
7. Important Deviation Ratio
8. Query Rate
9. Data Change Rate
10. Concomitant Medication Rate
11. Medical History Rate

This creates a comprehensive KRI dashboard showing both individual site performance and study-wide statistical benchmarks for clinical trial monitoring.

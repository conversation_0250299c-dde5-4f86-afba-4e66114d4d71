DECLARE @StudyId INT = 78;
----------------------------------------------------
WITH ing AS (
    SELECT d.id,
        d.StudyId,
        d.Name,
        i.Name AS ingestStatus,
        d.StatusId,
        EOMONTH(d.CreatedDate) AS CreatedMonth,
        pd.*,
        distIngestName = CASE
            WHEN d.Name <> LAG(d.Name) OVER (
                ORDER BY d.Name,
                    d.CreatedDate
            ) THEN 1
            ELSE 0
        END
    FROM Rrbm.IngestDefinitions d
        CROSS APPLY OPENJSON(JSON_QUERY(d.ParserMap, '$.AllDataElementMaps')) WITH (MapProperties nvarchar(max) AS JSON) elem
        CROSS APPLY OPENJSON(elem.MapProperties) WITH (
            Dest nvarchar(100) '$.Dest',
            ValueOverride nvarchar(100) '$.ValueOverride',
            Source nvarchar(100) '$.Source',
            IsCustomProp bit '$.IsCustomProp'
        ) pd
        LEFT JOIN Rrbm.IngestDefinitionStatuses i ON i.Id = d.StatusId
    WHERE d.StudyId = @StudyId
        AND d.FileCode <> 'DELETED'
)
SELECT distinct *,
    ingestCreatPerMonth = SUM(distIngestName) OVER (PARTITION BY CreatedMonth),
    activeIng = (
        SELECT TOP 1 COUNT(*)
        FROM (
                SELECT DISTINCT [Name]
                from ing
                where ingestStatus = 'Active'
            ) a
    ),
    elementType = CASE
        WHEN Dest = 'DataElementCode' THEN 'DataElementCode'
        WHEN IsCustomProp = 1 THEN 'CustomProperty'
    END,
    rowId = ROW_NUMBER() OVER (
        ORDER BY (
                Select NULL
            )
    )
FROM ing
DECLARE @StudyId INT = 1125;
--------------------------------------------

EVOM
------------------------------------
WITH enr AS (
	SELECT SiteCode,
		PatientCode,
		IntervalStartDate
	FROM Rrbm.StudyMetrics
	WHERE StudyId = @StudyId
		AND dataelementCode = 'EnrollmentStartDate'
		AND RecordStatusId = 1
		AND IntervalStartDate IS NOT NULL
),
scr AS (
	SELECT SiteCode,
		PatientCode,
		IntervalStartDate
	FROM Rrbm.StudyMetrics
	WHERE StudyId = @StudyId
		AND dataelementCode = 'ScreeningStartDate'
		AND RecordStatusId = 1
		AND IntervalStartDate IS NOT NULL
),
diary AS (
	SELECT SiteCode,
		PatientCode,
		[Value],
		IntervalStartDate AS DiaryDate
	FROM Rrbm.StudyMetrics
	WHERE StudyId = @StudyId
		AND dataelementCode = 'Diary'
		AND RecordStatusId = 1
		AND IntervalStartDate IS NOT NULL
) -- SELECT *
-- FROM enr e
-- LEFT JOIN diary d ON e.SiteCode = d.SiteCode AND e.PatientCode = d.PatientCode
-- -- WHERE d.DiaryDate >= e.IntervalStartDate
-- ORDER BY e.SiteCode, e.PatientCode, d.DiaryDate;
SELECT *
FROM scr s
LEFT JOIN enr e ON s.SiteCode = e.SiteCode AND s.PatientCode = e.PatientCode
LEFT JOIN diary d ON s.SiteCode = d.SiteCode AND s.PatientCode = d.PatientCode
-- WHERE d.DiaryDate >= s.IntervalStartDate